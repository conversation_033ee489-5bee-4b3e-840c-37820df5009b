using Domain.JobApplications;
using Domain.Jobs;
using Domain.Resumes;
using Domain.Users;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.JobApplications;

internal sealed class JobApplicationConfiguration : IEntityTypeConfiguration<JobApplication>
{
    public void Configure(EntityTypeBuilder<JobApplication> builder)
    {
        builder.HasKey(ja => ja.Id);

        builder.Property(ja => ja.Status)
            .HasConversion<int>();

        // Foreign key relationships with navigation properties properly linked
        builder.HasOne(ja => ja.Resume)
            .WithMany()
            .HasForeignKey(ja => ja.ResumeId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(ja => ja.Job)
            .WithMany()
            .HasForeignKey(ja => ja.JobId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(ja => ja.CreatedByUser)
            .WithMany()
            .HasForeign<PERSON>ey(ja => ja.CreatedBy)
            .OnDelete(DeleteBehavior.Restrict);

        // Unique constraint to prevent duplicate applications for the same resume-job combination
        builder.HasIndex(ja => new { ja.ResumeId, ja.JobId })
            .IsUnique();
    }
}
