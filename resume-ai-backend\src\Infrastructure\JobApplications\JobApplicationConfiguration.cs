using Domain.JobApplications;
using Domain.Jobs;
using Domain.Resumes;
using Domain.Users;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.JobApplications;

internal sealed class JobApplicationConfiguration : IEntityTypeConfiguration<JobApplication>
{
    public void Configure(EntityTypeBuilder<JobApplication> builder)
    {
        builder.HasKey(ja => ja.Id);

        builder.Property(ja => ja.Status)
            .HasConversion<int>();

        // Foreign key relationships
        builder.HasOne<Resume>()
            .WithMany()
            .HasForeignKey(ja => ja.ResumeId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne<Job>()
            .WithMany()
            .HasForeignKey(ja => ja.JobId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne<User>()
            .WithMany()
            .HasForeignKey(ja => ja.CreatedBy)
            .OnDelete(DeleteBehavior.Restrict);

        // Navigation properties
        builder.Navigation(ja => ja.Resume);
        builder.Navigation(ja => ja.Job);
        builder.Navigation(ja => ja.CreatedByUser);

        // Unique constraint to prevent duplicate applications for the same resume-job combination
        builder.HasIndex(ja => new { ja.ResumeId, ja.JobId })
            .IsUnique();
    }
}
