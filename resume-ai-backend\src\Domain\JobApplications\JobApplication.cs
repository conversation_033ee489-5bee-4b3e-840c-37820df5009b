using Domain.Jobs;
using Domain.Resumes;
using Domain.Users;
using SharedKernel;

namespace Domain.JobApplications;

public class JobApplication : BaseEntity
{
    public Guid ResumeId { get; set; }
    public Guid JobId { get; set; }
    public Guid CreatedBy { get; set; }
    public JobApplicationStatus Status { get; set; }

    // Navigation properties
    public Resume Resume { get; set; }
    public Job Job { get; set; }
    public User CreatedByUser { get; set; }

    public static JobApplication Create(Guid resumeId, Guid jobId, Guid createdBy)
    {
        var jobApplication = new JobApplication
        {
            ResumeId = resumeId,
            JobId = jobId,
            CreatedBy = createdBy,
            Status = JobApplicationStatus.Applied
        };

        jobApplication.Raise(new JobApplicationCreatedDomainEvent(jobApplication.Id));

        return jobApplication;
    }

    public void UpdateStatus(JobApplicationStatus newStatus)
    {
        if (Status != newStatus)
        {
            JobApplicationStatus oldStatus = Status;
            Status = newStatus;
            
            Raise(new JobApplicationStatusChangedDomainEvent(Id, oldStatus, newStatus));
        }
    }
}
