// <auto-generated/>

namespace Projects;

[global::System.CodeDom.Compiler.GeneratedCode("Aspire.Hosting", null)]
[global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage(Justification = "Generated code.")]
[global::System.Diagnostics.DebuggerDisplay("Type = {GetType().Name,nq}, ProjectPath = {ProjectPath}")]
public class Web_Api : global::Aspire.Hosting.IProjectMetadata
{
    public string ProjectPath => """C:\Users\<USER>\OneDrive\Desktop\All\Projects\resume-ai\resume-ai-backend\src\Web.Api\Web.Api.csproj""";
}
