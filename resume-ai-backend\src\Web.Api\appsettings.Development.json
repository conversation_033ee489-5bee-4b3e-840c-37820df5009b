{"ConnectionStrings": {"Database": "Host=********;Port=5432;Database=resume-ai;Username=********;Password=********;Include Error Detail=true"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Seq"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "Seq", "Args": {"ServerUrl": "http://seq:5341"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "Jwt": {"Secret": "super-duper-secret-value-that-should-be-in-user-secrets", "Issuer": "resume-ai", "Audience": "developers", "ExpirationInMinutes": 60}}