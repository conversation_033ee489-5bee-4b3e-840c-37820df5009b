using Domain.Resumes;
using Domain.Todos;
using Domain.Users;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Resumes;

internal sealed class ResumeConfiguration : IEntityTypeConfiguration<Resume>
{
    public void Configure(EntityTypeBuilder<Resume> builder)
    {
        builder.HasKey(t => t.Id);

        builder.HasOne<User>().WithMany().HasForeignKey(t => t.UserId);

        // Configure optional self-referencing foreign key for parent resume
        builder.HasOne<Resume>()
            .WithMany()
            .HasForeignKey(t => t.ParentId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
