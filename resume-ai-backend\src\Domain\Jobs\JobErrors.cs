using SharedKernel;

namespace Domain.Jobs;

public static class JobErrors
{
    public static Error NotFound(Guid jobId) => Error.NotFound(
        "Jobs.NotFound",
        $"The job with the Id = '{jobId}' was not found");

    public static Error UserNotFound(Guid userId) => Error.NotFound(
        "Jobs.UserNotFound",
        $"The user with Id = '{userId}' was not found");

    public static Error EmptyJobTitle() => Error.Problem(
        "Jobs.EmptyJobTitle",
        "Job title cannot be empty or null");

    public static Error EmptyJobDescription() => Error.Problem(
        "Jobs.EmptyJobDescription",
        "Job description cannot be empty or null");

    public static Error InvalidJobPostingUrl(string url) => Error.Problem(
        "Jobs.InvalidJobPostingUrl",
        $"The job posting URL '{url}' is not valid");

    public static Error InvalidCompanyUrl(string url) => Error.Problem(
        "Jobs.InvalidCompanyUrl",
        $"The company URL '{url}' is not valid");

    public static Error UnauthorizedAccess(Guid jobId, Guid userId) => Error.Problem(
        "Jobs.UnauthorizedAccess",
        $"User '{userId}' is not authorized to access job '{jobId}'");

    public static Error AlreadyApplied(Guid jobId) => Error.Problem(
        "Jobs.AlreadyApplied",
        $"Job '{jobId}' has already been applied to");

    public static Error CannotDeleteJobWithApplications(Guid jobId) => Error.Problem(
        "Jobs.CannotDeleteJobWithApplications",
        $"Cannot delete job '{jobId}' because it has associated job applications");

    public static Error AppliedDateInFuture() => Error.Problem(
        "Jobs.AppliedDateInFuture",
        "Applied date cannot be in the future");

    public static Error DuplicateJob(string jobTitle, string companyUrl) => Error.Conflict(
        "Jobs.DuplicateJob",
        $"A job with title '{jobTitle}' at company '{companyUrl}' already exists");
}
