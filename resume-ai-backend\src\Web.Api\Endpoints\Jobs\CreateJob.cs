using Application.Abstractions.Data;
using Domain.Jobs;
using SharedKernel;
using Web.Api.Extensions;
using Web.Api.Infrastructure;

namespace Web.Api.Endpoints.Jobs;

internal sealed class CreateJob : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("jobs", CreateJobAsync)
            .WithTags(Tags.Jobs)
            .WithName("CreateJob")
            .Produces<Guid>()
            .ProducesValidationProblem()
            .RequireAuthorization();
    }

    private static async Task<IResult> CreateJobAsync(
        CreateJobRequest request,
        IApplicationDbContext context,
        IDateTimeProvider dateTimeProvider,
        CancellationToken cancellationToken)
    {
        var job = new Job
        {
            Id = Guid.NewGuid(),
            UserId = request.UserId, // In real app, get from user context
            JobTitle = request.JobTitle,
            JobDescription = request.JobDescription,
            JobPostingUrl = request.JobPostingUrl,
            CompanyUrl = request.CompanyUrl,
            AppliedAt = request.AppliedAt,
            CreatedAt = dateTimeProvider.UtcNow,
            IsDeleted = false
        };

        context.Jobs.Add(job);
        await context.SaveChangesAsync(cancellationToken);

        return Results.Created($"/jobs/{job.Id}", job.Id);
    }
}

public sealed record CreateJobRequest(
    Guid UserId,
    string JobTitle,
    string JobDescription,
    string JobPostingUrl,
    string CompanyUrl,
    DateTime? AppliedAt);
