{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"SharedKernel/1.0.0": {"dependencies": {"SonarAnalyzer.CSharp": "10.12.0.118525"}, "runtime": {"SharedKernel.dll": {}}}, "SonarAnalyzer.CSharp/10.12.0.118525": {}}}, "libraries": {"SharedKernel/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "SonarAnalyzer.CSharp/10.12.0.118525": {"type": "package", "serviceable": true, "sha512": "sha512-uP38bsYegQBk8WOM6LYIAht6hrA7tcJgep/WuifPJjhjtjysPUP/iM/c1+P2+llNIDmm1s8Xh86+WG3K71eycw==", "path": "sonaranalyzer.csharp/10.12.0.118525", "hashPath": "sonaranalyzer.csharp.10.12.0.118525.nupkg.sha512"}}}