using Application.Abstractions.Authentication;
using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Domain.Resumes;
using Domain.Todos;
using Domain.Users;
using Microsoft.EntityFrameworkCore;
using SharedKernel;

namespace Application.Resumes.Create;

internal sealed class CreateResumeCommandHandler(
    IApplicationDbContext context,
    IUserContext userContext)
    : ICommandHandler<CreateResumeCommand, Guid>
{
    public async Task<Result<Guid>> Handle(CreateResumeCommand command, CancellationToken cancellationToken)
    {
        if (userContext.UserId != command.UserId)
        {
            return Result.Failure<Guid>(UserErrors.Unauthorized());
        }

        User? user = await context.Users.AsNoTracking()
            .SingleOrDefaultAsync(u => u.Id == command.UserId, cancellationToken);

        if (user is null)
        {
            return Result.Failure<Guid>(UserErrors.NotFound(command.UserId));
        }

        var resume = new Resume
        {
            UserId = user.Id,
            ParentId = command.ParentId,
            ResumeContent = command.ResumeContent,
        };

        resume.Raise(new ResumeCreatedDomainEvent(resume.Id));

        context.Resumes.Add(resume);

        await context.SaveChangesAsync(cancellationToken);

        return resume.Id;
    }
}
