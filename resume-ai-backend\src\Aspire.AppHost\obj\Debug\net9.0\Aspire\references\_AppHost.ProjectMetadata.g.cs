// <auto-generated/>

namespace Projects;

[global::System.CodeDom.Compiler.GeneratedCode("Aspire.Hosting", null)]
[global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage(Justification = "Generated code.")]
[global::System.Diagnostics.DebuggerDisplay("Type = {GetType().Name,nq}, ProjectPath = {ProjectPath}")]
public class Aspire_AppHost
{
    private Aspire_AppHost() { }
    public static string ProjectPath => """C:\Users\<USER>\OneDrive\Desktop\All\Projects\resume-ai\resume-ai-backend\src\Aspire.AppHost""";
}
