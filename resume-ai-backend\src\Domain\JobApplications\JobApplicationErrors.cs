using SharedKernel;

namespace Domain.JobApplications;

public static class JobApplicationErrors
{
    public static Error NotFound(Guid jobApplicationId) => Error.NotFound(
        "JobApplications.NotFound",
        $"The job application with the Id = '{jobApplicationId}' was not found");

    public static Error AlreadyExists(Guid resumeId, Guid jobId) => Error.Problem(
        "JobApplications.AlreadyExists",
        $"A job application already exists for Resume '{resumeId}' and Job '{jobId}'");

    public static Error InvalidStatusTransition(JobApplicationStatus currentStatus, JobApplicationStatus newStatus) => Error.Problem(
        "JobApplications.InvalidStatusTransition",
        $"Cannot change status from '{currentStatus}' to '{newStatus}'");

    public static Error ResumeNotFound(Guid resumeId) => Error.NotFound(
        "JobApplications.ResumeNotFound",
        $"The resume with Id = '{resumeId}' was not found");

    public static Error JobNotFound(Guid jobId) => Error.NotFound(
        "JobApplications.JobNotFound",
        $"The job with Id = '{jobId}' was not found");
}
